package com.hg.engine.api.websocket;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * WebSocket管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/websocket")
@RequiredArgsConstructor
@Tag(name = "WebSocket管理", description = "WebSocket连接管理和监控接口")
public class WebSocketManagementController {


}
