package com.hg.engine.api.tp;

import com.hg.engine.shared.exception.ServeException;
import com.hg.engine.shared.framework.web.annotation.ResultBodyConvert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 管道系统控制器，用于处理管道系统配置相关的HTTP请求
 * 接收前端可视化编辑器导出的管道系统配置
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "图（系统）")
@ResultBodyConvert
public class SystemController {
    private final com.hg.engine.application.tp.service.SystemService systemService;

    /**
     * 接收前端可视化编辑器导出的管道系统配置
     *
     * @param pipelineSystem 管道系统配置数据
     */
    @Operation(summary = "创建系统或更新", description = "接收前端可视化编辑器导出的管道系统配置")
    @PostMapping()
    public void createOrUpdate(@RequestBody com.hg.engine.application.tp.dto.SystemDTO pipelineSystem) {
        if (Objects.isNull(pipelineSystem) || Objects.isNull(pipelineSystem.getId())) {
            log.error("管道系统配置数据为空");
            throw new ServeException("管道系统配置数据为空");
        }
        systemService.createOrUpdate(pipelineSystem);
    }

    @Operation(summary = "获取图（系统）", description = "根据id获取图")
    @GetMapping("/{id}")
    public com.hg.engine.application.tp.dto.SystemDTO getSystem(@PathVariable String id) {
        return systemService.getSystem(id);
    }

}
