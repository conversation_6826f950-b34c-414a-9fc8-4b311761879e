package com.hg.engine.api.tp;

import com.alibaba.fastjson2.JSONObject;
import com.hg.engine.application.tp.dto.NodeEventDTO;
import com.hg.engine.application.tp.event.service.EventPublishingService;
import com.hg.engine.shared.exception.ServeException;
import com.hg.engine.shared.framework.web.annotation.ResultBodyConvert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@RestController
@ResultBodyConvert
@Tag(name = "节点", description = "节点")
@RequestMapping("/node")
@RequiredArgsConstructor
public class NodeController {

    private final EventPublishingService eventPublishingService;

    @Operation(summary = "接收事件", description = "接收前端可视化编辑器发送的事件")
    @PostMapping("/event")
    public void receiveEvent(@RequestBody NodeEventDTO dto) {
        JSONObject parameters = dto.getParameters();
        if (Objects.isNull(parameters.get("id"))) {
            throw new ServeException("节点id不能为空");
        }
         eventPublishingService.publishEvent(dto);
    }

}
