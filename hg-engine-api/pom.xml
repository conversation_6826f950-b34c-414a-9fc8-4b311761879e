<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.hg.engine</groupId>
        <artifactId>hg-engine-server</artifactId>
        <version>1.0.0</version>
    </parent>
    <artifactId>hg-engine-api</artifactId>

    <dependencies>
        <!-- 依赖application和shared模块 -->
        <dependency>
            <groupId>com.hg.engine</groupId>
            <artifactId>hg-engine-application</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hg.engine</groupId>
            <artifactId>hg-engine-shared</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hg.engine</groupId>
            <artifactId>hg-engine-infrastructure</artifactId>
        </dependency>

        <!-- Spring Boot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Swagger/OpenAPI -->
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>


    </dependencies>
</project>
