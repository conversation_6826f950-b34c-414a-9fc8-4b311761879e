package com.hg.engine.application.tp.service.impl;

import com.hg.engine.application.tp.convertor.SystemDTOConverter;
import com.hg.engine.application.tp.convertor.SystemGraphConverter;
import com.hg.engine.application.tp.dto.SystemDTO;
import com.hg.engine.application.tp.service.SystemService;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.System;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.domain.tp.service.NodeDomainService;
import com.hg.engine.domain.tp.service.impl.SystemDomainServiceImpl;
import com.hg.engine.shared.exception.ServeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class SystemServiceImpl implements SystemService {
    private final NodeDomainService nodeDomainService;
    private final NodeRepository nodeRepository;
    private final SystemGraphConverter systemGraphConverter;
    // 图
    private final SystemDomainServiceImpl systemDomainService;
    private final SystemDTOConverter systemDTOConverter;

    @Override
    @Transactional
    public String createOrUpdate(SystemDTO dto) {
        System system = systemDTOConverter.dto2Domain(dto);
        //判断system是否存在
        System dbSystem = systemDomainService.getById(system.getId());
        //更新图数据含节点
        if (Objects.nonNull(dbSystem)) {
            return this.updateGraph(system, dto);
        }
        // 保存系统数据
        systemDomainService.save(system);
        List<Node> nodes = systemGraphConverter.convertToNodes(dto);
        nodeRepository.saveNodes(nodes);
        return dto.getId();
    }

    /**
     * 更新图
     *
     * @param target 系统数据
     * @param dto    前端数据
     */
    private String updateGraph(System target, SystemDTO dto) {
        // 根据id更新系统数据
        systemDomainService.updateById(target);
        List<Node> nodes = systemGraphConverter.convertToNodes(dto);
        // 更新图谱数据
        nodeDomainService.updateNodes(nodes, target.getId());
        return target.getId();
    }


    @Override
    public SystemDTO getSystem(String id) {
        System system = systemDomainService.getById(id);
        if (Objects.isNull(system)) {
            throw new ServeException("系统不存在");
        }
        SystemDTO dto = systemDTOConverter.domain2Dto(system);
        List<Node> nodes = nodeRepository.findSystemId(id);
        systemGraphConverter.generateItems(dto, nodes);
        return dto;
    }

}
