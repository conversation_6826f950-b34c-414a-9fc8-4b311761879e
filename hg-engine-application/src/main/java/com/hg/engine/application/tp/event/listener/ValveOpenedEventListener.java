package com.hg.engine.application.tp.event.listener;

import cn.hutool.core.collection.CollUtil;
import com.hg.engine.application.tp.event.service.EventPublishingService;
import com.hg.engine.domain.tp.event.PipeFilledEvent;
import com.hg.engine.domain.tp.event.PipeStoppedEvent;
import com.hg.engine.domain.tp.event.ValveOpenedEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.domain.tp.repository.NodeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 阀门打开事件监听器
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ValveOpenedEventListener extends NodeEventListener<ValveOpenedEvent> {

    private final NodeRepository nodeRepository;

    private final EventPublishingService eventPublishingService;


    @Override
    protected void handleEvent(ValveOpenedEvent event) {
        // 获取当前阀门节点
        Valve node = (Valve) nodeRepository.getById(event.getId());
        node.setOpen(true);
        node.setOpenMode(event.getOpenMode());
        nodeRepository.updateNode(node);
        // 判断是否有水
        List<Node> lastNodeList = nodeRepository.getLastNodeList(node.getId());
        boolean isWater = false;
        for (Node last : lastNodeList) {
            if (last instanceof Pipe) {
                Pipe pipe = (Pipe) last;
                if (Boolean.TRUE.equals(pipe.getFlow())) {
                    isWater = true;
                    break;
                }
            }
        }
        // 如果没有上游结点(意味着是一个根节点) || 或者上游节点有流动则注水
        if (isWater || lastNodeList.isEmpty()) {
            List<Node> nextNodeList = nodeRepository.getNextNodeList(node.getId());
            List<String> openModeList = event.getOpenMode();

            for (Node nextNode : nextNodeList) {
                if (nextNode instanceof Pipe) {
                    Pipe pipe = (Pipe) nextNode;

                    // 如果 openModeList 为空，走正常阀门逻辑
                    if (CollUtil.isEmpty(openModeList)) {
                        PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                        eventPublishingService.publishEvent(pipeFilledEvent);
                    } else {
                        // 如果 openModeList 包含该管道，注水
                        if (openModeList.contains(pipe.getId())) {
                            PipeFilledEvent pipeFilledEvent = new PipeFilledEvent(pipe.getId());
                            eventPublishingService.publishEvent(pipeFilledEvent);
                        } else {
                            // 停止不在 openModeList 中的管道流动
                            PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(pipe.getId());
                            eventPublishingService.publishEvent(pipeStoppedEvent);
                        }
                    }
                }
            }
        }
    }
}
