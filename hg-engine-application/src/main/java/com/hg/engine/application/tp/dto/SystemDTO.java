package com.hg.engine.application.tp.dto;

import lombok.Data;

import java.util.Map;

/**
 * 管道系统配置DTO类，用于接收前端可视化编辑器导出的管道系统配置JSON数据
 * 前端通过可视化编辑器绘制管道系统后，将配置导出为JSON格式并发送到后端
 *
 * <AUTHOR>
 */
@Data
public class SystemDTO {

    /**
     * 系统唯一标识符
     */
    private String id;

    /**
     * 系统名称
     */
    private String name;

    /**
     * 系统界面配置
     * 包含宽度、高度、背景色等界面相关配置
     */
    private Map<String, Object> profile;

    /**
     * 组件列表
     * Key为组件唯一标识符，Value为组件详细信息
     * 组件类型包括阀门(hgValve)和管道(hgPipe)
     */
    private Map<String, Object> items;

    /**
     * 系统变量
     * 存储系统中使用的变量信息
     */
    private Map<String, Object> variables;

    /**
     * SVG内容
     * 系统的SVG图形表示
     */
    private String svgContent;

    /**
     * 系统类型
     */
    private String type;
}
