package com.hg.engine.application.tp.event.service;

import com.hg.engine.application.tp.dto.NodeEventDTO;
import com.hg.engine.application.tp.event.NodeEventFactory;
import com.hg.engine.domain.tp.event.NodeEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;


/**
 * 事件发布服务类
 */
@Service
@RequiredArgsConstructor
public class EventPublishingService {

    private final ApplicationEventPublisher eventPublisher;


    private final NodeEventFactory nodeEventFactory;


    /**
     * 根据节点发布相应事件
     */
    public void publishEvent(NodeEventDTO dto) {
        NodeEvent nodeEvent = nodeEventFactory.buildEvent(dto);
        eventPublisher.publishEvent(nodeEvent);
    }


    /**
     * 根据节点发布相应事件
     */
    public void publishEvent(NodeEvent nodeEvent) {
        eventPublisher.publishEvent(nodeEvent);
    }

}