package com.hg.engine.application.tp.convertor;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.NodeClassType;
import com.hg.engine.shared.exception.ServeException;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface NodeDTOConverter {

    /**
     * 前端json转换为Node
     *
     * @param object json
     * @return Node
     */
    default Node json2Node(Object object) {
        String jsonStr = JSON.toJSONString(object);
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        String nodeId = (String) jsonObject.get("nodeId");
        String id = (String) jsonObject.get("id");
        String type = (String) jsonObject.get("type");
        String valveType = (String) jsonObject.get("valveType");
        Class<? extends Node> classByType = NodeClassType.getClassByType(type);
        if (null == classByType) {
            throw new ServeException("tp.node.not_found");
        }
        Node node = JSON.parseObject(jsonStr, NodeClassType.getClassByType(type));
        node.setId(nodeId);
        node.setViewId(id);
        return node;
    }
}
