package com.hg.engine.application.tp.event.listener;

import com.hg.engine.domain.tp.event.NodeEvent;
import org.springframework.context.ApplicationListener;

/**
 * 节点事件监听器基类
 */
public abstract class NodeEventListener<T extends NodeEvent> implements ApplicationListener<T> {

    @Override
    public void onApplicationEvent(T event) {
        handleEvent(event);
    }
    /**
     * 处理具体的事件
     *
     * @param event 事件对象
     */
    protected abstract void handleEvent(T event);
}