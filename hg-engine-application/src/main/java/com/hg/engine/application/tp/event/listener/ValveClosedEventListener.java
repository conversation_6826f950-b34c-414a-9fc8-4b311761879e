package com.hg.engine.application.tp.event.listener;

import com.hg.engine.domain.tp.event.PipeStoppedEvent;
import com.hg.engine.domain.tp.event.ValveClosedEvent;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.domain.tp.repository.NodeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 阀门关闭事件监听器
 */
@Component
@RequiredArgsConstructor
public class ValveClosedEventListener extends NodeEventListener<ValveClosedEvent> {

    private final ApplicationEventPublisher eventPublisher;

    private final NodeRepository nodeRepository;
    @Override
    protected void handleEvent(ValveClosedEvent event) {
        // 获取当前阀门节点
        Valve node = (Valve) nodeRepository.getById(event.getId());
        // 设置阀门为关闭状态
        node.setOpen(false);
        nodeRepository.updateNode(node);

        // 获取下游节点列表
        List<Node> nextNodeList = nodeRepository.getNextNodeList(node.getId());

        // 分发下游事件
        for (Node nextNode : nextNodeList) {
            if (nextNode instanceof Pipe) {
                // 如果是水管，创建并发布 PipeStoppedEvent
                PipeStoppedEvent pipeStoppedEvent = new PipeStoppedEvent(nextNode.getId());
                eventPublisher.publishEvent(pipeStoppedEvent);
            }
        }
    }
}
