package com.hg.engine.application.tp.event;

import com.alibaba.fastjson2.JSONObject;
import com.hg.engine.application.tp.dto.NodeEventDTO;
import com.hg.engine.domain.tp.event.NodeEvent;
import com.hg.engine.domain.tp.event.NodeEventTypeMapping;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class NodeEventFactory implements ApplicationContextAware {

    private final Map<String, Class<? extends NodeEvent>> registry = new HashMap<>();


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) {
        // 获取所有 Bean 定义（包括未被实例化的类）
        // 只扫描被 @NodeEventTypeMapping 标注的类
        ClassPathScanningCandidateComponentProvider scanner = new ClassPathScanningCandidateComponentProvider(false);
        scanner.addIncludeFilter(new AnnotationTypeFilter(NodeEventTypeMapping.class));

        // 替换为你的事件类所在包路径
        String basePackage = "com.hg.engine.domain.tp.event";

        for (BeanDefinition bd : scanner.findCandidateComponents(basePackage)) {
            try {
                Class<?> clazz = Class.forName(bd.getBeanClassName());
                if (NodeEvent.class.isAssignableFrom(clazz)) {
                    NodeEventTypeMapping mapping = clazz.getAnnotation(NodeEventTypeMapping.class);
                    registry.put(mapping.value(), (Class<? extends NodeEvent>) clazz);
                }
            } catch (ClassNotFoundException e) {
                throw new RuntimeException("加载事件类失败", e);
            }
        }
    }

    public NodeEvent buildEvent(NodeEventDTO dto) {
        Class<? extends NodeEvent> eventClass = registry.get(dto.getEventType());
        JSONObject parameters = dto.getParameters();
        return parameters.toJavaObject(eventClass);
    }
}
