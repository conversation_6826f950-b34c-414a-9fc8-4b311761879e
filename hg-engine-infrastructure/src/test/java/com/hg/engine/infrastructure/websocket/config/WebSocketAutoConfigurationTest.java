package com.hg.engine.infrastructure.websocket.config;

import com.hg.engine.domain.websocket.repository.WebSocketSessionRepository;
import com.hg.engine.domain.websocket.service.TopicSubscriptionService;
import com.hg.engine.domain.websocket.service.WebSocketDomainService;
import com.hg.engine.domain.websocket.service.WebSocketMessageSender;
import com.hg.engine.infrastructure.websocket.handler.WebSocketChannelInitializer;
import com.hg.engine.infrastructure.websocket.handler.WebSocketServerHandler;
import com.hg.engine.infrastructure.websocket.manager.WebSocketSessionManager;
import com.hg.engine.infrastructure.websocket.server.NettyWebSocketServer;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WebSocket自动配置测试
 */
@SpringJUnitConfig(WebSocketAutoConfiguration.class)
@TestPropertySource(properties = {
    "netty.websocket.enabled=true",
    "netty.websocket.port=8081",
    "netty.websocket.path=/ws"
})
public class WebSocketAutoConfigurationTest {

    @Test
    public void testWebSocketAutoConfiguration() {
        // 这个测试主要验证配置类能够正常加载
        // 实际的Bean创建测试需要完整的Spring上下文
        assertTrue(true, "WebSocketAutoConfiguration加载成功");
    }
}
