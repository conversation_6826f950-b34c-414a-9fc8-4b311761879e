package com.hg.engine.infrastructure.persistence;

import cn.hutool.core.collection.CollUtil;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.infrastructure.persistence.convertor.NodeConverter;
import com.hg.engine.infrastructure.persistence.po.NodePO;
import com.hg.engine.infrastructure.persistence.repository.db.neo4j.NodeMapper;
import com.hg.engine.shared.exception.ServeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NodeRepositoryImpl implements NodeRepository {

    private final NodeMapper nodeMapper;

    private final NodeConverter nodeConverter;

    @Override
    public Node findById(String id) {
        Optional<NodePO> po = nodeMapper.findById(id);
        NodePO nodePO = po.orElse(null);
        return nodeConverter.po2domain(nodePO);
    }

    @Override
    public List<Node> findSystemId(String systemId) {
        List<NodePO> nodePOs = nodeMapper.findFullGraphBySystemId(systemId);
        return nodeConverter.po2domainList(nodePOs);
    }

    @Override
    public List<Node> findChildrenByParentId(String parentId) {
        List<NodePO> nodePOs = nodeMapper.findChildrenByParentId(parentId);
        return nodeConverter.po2domainList(nodePOs);
    }

    @Override
    public List<Node> getLastNodeList(String id) {
        List<NodePO> lastNode = nodeMapper.getLastNodeList(id);
        return nodeConverter.po2domainList(lastNode);
    }

    @Override
    public Node getNodeWithRelations(String id) {
        NodePO po = nodeMapper.getById(id);
        if (null == po) {
            throw new ServeException("节点不存在");
        }
        Node node = nodeConverter.po2domain(po);
        List<NodePO> lastNode = nodeMapper.getLastNodeList(id);
        if (CollUtil.isNotEmpty(lastNode)) {
            List<Node> lastNodes = nodeConverter.po2domainList(lastNode);
            node.setLastNode(lastNodes);
        }
        List<NodePO> nextNode = nodeMapper.getNextNodeList(id);
        if (CollUtil.isNotEmpty(nextNode)) {
            List<Node> nextNodes = nodeConverter.po2domainList(nextNode);
            node.setNextNode(nextNodes);
        }
        return node;
    }

    @Override
    public void save(Node node) {
        NodePO nodes = nodeConverter.domain2po(node);
        nodeMapper.save(nodes);
    }

    @Override
    public void saveNodes(List<Node> nodes) {
        // 第一步：找到所有节点的连接关系
        List<NodePO> nodePOS = nodeConverter.domain2poList(nodes);
        nodeMapper.saveAll(nodePOS);
    }


    @Override
    public void delete(Node node) {
        NodePO nodePO = nodeConverter.domain2po(node);
        nodeMapper.delete(nodePO);
    }


    @Override
    public void updateNodes(List<Node> nodeList) {
        for (Node node : nodeList) {
            // 将 Node 转换为 NodePO
            NodePO nodePO = nodeConverter.domain2po(node);

            // 检查节点是否存在
            Optional<NodePO> optionalNodePO = nodeMapper.findById(node.getId());
            if (!optionalNodePO.isPresent()) {
                throw new ServeException("节点不存在: " + node.getId());
            }

            // 更新节点
            nodeMapper.updateNode(nodePO);
        }
    }

    @Override
    public void deleteSystemId(String systemId) {
        if (systemId == null || systemId.isEmpty()) {
            log.warn("传入的 systemId 为空，无法执行删除操作");
            return;
        }
        nodeMapper.deleteBySystemId(systemId);
    }

    @Override
    public void updateNode(Node node) {
        NodePO nodePO = nodeConverter.domain2po(node);
        nodeMapper.save(nodePO);
    }

    @Override
    public List<Node> getNextNodeList(String id) {
        List<NodePO> nextNodeList = nodeMapper.getNextNodeList(id);
        return nodeConverter.po2domainList(nextNodeList);
    }

    @Override
    public Node getById(String id) {
        NodePO byId = nodeMapper.getById(id);
        if (Objects.isNull(byId)) {
            throw new ServeException("节点不存在");
        }
        return nodeConverter.po2domain(byId);
    }

}
