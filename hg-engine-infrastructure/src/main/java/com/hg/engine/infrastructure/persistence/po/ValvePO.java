package com.hg.engine.infrastructure.persistence.po;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Node;

import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Node("valve")
@Data
public class ValvePO extends NodePO {

    /**
     * 阀门类型
     * normal: 普通阀门
     * three-way-split: 三通分流阀
     * three-way-merge: 三通合流阀
     */
    private String valveType;

    /**
     * 阀门开关状态
     * true: 开启
     * false: 关闭
     */
    private Boolean open;

    /**
     * 阀门开启模式，定义阀门在开启状态下流体的流向到指定管道
     * 仅在对于三通阀，指定开启时流体通过的管道
     */
    private List<String> openMode;
}
