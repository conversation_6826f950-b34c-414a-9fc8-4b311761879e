package com.hg.engine.infrastructure.persistence.po;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.neo4j.core.schema.Node;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Node("pipe")
@Data
public class PipePO extends NodePO {

    // 是否流动
    private Boolean flow;

    // 流动方向 forward/reverse
    private String flowDirection;

}
