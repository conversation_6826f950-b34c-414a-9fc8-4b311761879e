package com.hg.engine.infrastructure.persistence.repository.db.neo4j;

import com.hg.engine.infrastructure.persistence.po.NodePO;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface NodeMapper extends Neo4jRepository<NodePO, String> {

    @Query("""
            MATCH path = (n:node {systemId: $systemId})-[:CONNECTED_TO*0..]->(m:node)
            RETURN DISTINCT n, collect(relationships(path)) AS rels, collect(m) AS nodes
            """)
    List<NodePO> findFullGraphBySystemId(@Param("systemId") String systemId);


    @Query("""
            MATCH (parent:node {id: $parentId})-[:CONNECTED_TO]->(child:node)
            RETURN child
            """)
    List<NodePO> findChildrenByParentId(String parentId);

    @Query("MATCH (n:node {id: $id}) RETURN n")
    NodePO getById(String id);

    /**
     * 向上查找一层所有节点
     *
     * @param id 节点ID
     * @return 上游节点列表
     */
    @Query("""
            MATCH (parent:node)-[:CONNECTED_TO]->(child:node {id: $id})
            RETURN parent
            """)
    List<NodePO> getLastNodeList(@Param("id") String id);

    /**
     * 向下查找一层所有节点
     *
     * @param id 节点ID
     * @return 下游节点列表
     */
    @Query("""
            MATCH (parent:node {id: $id})-[:CONNECTED_TO]->(child:node)
            RETURN child
            """)
    List<NodePO> getNextNodeList(String id);


    @Query("MATCH (n:node {id: $id}) " +
            "OPTIONAL MATCH (n)-[r:CONNECTED_TO]->(target) " +
            "RETURN n, collect(target) as targets")
    NodePO getNodeWithRelations(@Param("id") String id);


    // 更新方法
    @Query("MATCH (n:node {id: $nodePO.id}) " +
            "SET n.type = $nodePO.type, " +
            "n.name = $nodePO.name, " +
            "n.systemId = $nodePO.systemId, " +
            "n.property = $nodePO.property " +
            "RETURN n")
    NodePO updateNode(@Param("nodePO") NodePO nodePO);

    // 创建方法
    @Query("CREATE (n:node {id: $nodePO.id, type: $nodePO.type, name: $nodePO.name, " +
            "systemId: $nodePO.systemId, property: $nodePO.property}) " +
            "RETURN n")
    NodePO createNode(@Param("nodePO") NodePO nodePO);

    /**
     * 根据系统id删除所有节点以及关系
     */
    @Query("MATCH (n{systemId: $systemId}) DETACH DELETE n")
    void deleteBySystemId(@Param("systemId") String systemId);
}
