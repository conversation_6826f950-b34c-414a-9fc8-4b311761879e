package com.hg.engine.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * @TableName system
 */
@TableName(value = "system", autoResultMap = true)
@Data
public class SystemPO implements Serializable {
    /**
     * 系统唯一标识符
     */
    @TableId(value = "id")
    private String id;

    /**
     * 系统名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 系统界面配置
     * 包含宽度、高度、背景色等界面相关配置
     */
    @TableField(value = "profile", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> profile;

    /**
     * 组件列表
     * Key为组件唯一标识符，Value为组件详细信息
     * 组件类型包括阀门(hgValve)和管道(hgPipe)
     */
    @TableField(exist = false)
    private Map<String, Object> items;

    /**
     * 系统变量
     * 存储系统中使用的变量信息
     */
    @TableField(value = "variables", typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> variables;

    /**
     * SVG内容
     * 系统的SVG图形表示
     */
    @TableField(value = "svg_content")
    private String svgContent;

    /**
     * 系统类型
     */
    @TableField(value = "type")
    private String type;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;
}
