package com.hg.engine.infrastructure.persistence.convertor;


import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.infrastructure.persistence.po.NodePO;
import com.hg.engine.infrastructure.persistence.po.PipePO;
import com.hg.engine.infrastructure.persistence.po.ValvePO;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 */
@Component
public class NodeConvertFactory {

    private final Map<Class<? extends Node>, Supplier<? extends NodePO>> DOMAIN_2_PO = new HashMap<>();
    private final Map<Class<? extends NodePO>, Supplier<? extends Node>> PO_2_DOMAIN = new HashMap<>();

    public NodeConvertFactory() {
        DOMAIN_2_PO.put(Valve.class, ValvePO::new);
        PO_2_DOMAIN.put(ValvePO.class, Valve::new);
//       -----
        DOMAIN_2_PO.put(Pipe.class, PipePO::new);
        PO_2_DOMAIN.put(PipePO.class, Pipe::new);

    }
    public Node createNode(NodePO b) {
        Supplier<? extends Node> supplier = PO_2_DOMAIN.get(b.getClass());
        if (supplier == null) {
            throw new IllegalArgumentException("Unknown B: " + b.getClass());
        }
        return supplier.get();
    }

    public NodePO createNodePo(Node a) {
        Supplier<? extends NodePO> supplier = DOMAIN_2_PO.get(a.getClass());
        if (supplier == null) {
            throw new IllegalArgumentException("Unknown A: " + a.getClass());
        }
        return supplier.get();
    }

}
