package com.hg.engine.infrastructure.persistence.convertor;

import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.infrastructure.persistence.po.NodePO;
import com.hg.engine.infrastructure.persistence.po.PipePO;
import com.hg.engine.infrastructure.persistence.po.ValvePO;
import org.mapstruct.Mapper;

import java.util.List;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public abstract class NodeConverter {
    public NodePO domain2po(Node domain) {
        if (domain instanceof Valve) {
            return valve2po((Valve) domain);
        } else if (domain instanceof Pipe) {
            return pipe2po((Pipe) domain);
        }
        throw new IllegalArgumentException("Unsupported domain type: " + domain.getClass());
    }

    public Node po2domain(NodePO po) {
        if (po instanceof ValvePO) {
            return po2valve((ValvePO) po);
        } else if (po instanceof PipePO) {
            return po2pipe((PipePO) po);
        }
        throw new IllegalArgumentException("Unsupported PO type: " + po.getClass());
    }

    // -------------- 列表转换 ------------------

    public List<Node> po2domainList(List<NodePO> pos) {
        return pos.stream().map(this::po2domain).collect(Collectors.toList());
    }

    public List<NodePO> domain2poList(List<Node> domains) {
        return domains.stream().map(this::domain2po).collect(Collectors.toList());
    }

    // -------------- MapStruct 子类映射 必须写 ------------------
    public abstract ValvePO valve2po(Valve valve);

    public abstract Valve po2valve(ValvePO po);


    public abstract PipePO pipe2po(Pipe pipe);

    public abstract Pipe po2pipe(PipePO po);
}
