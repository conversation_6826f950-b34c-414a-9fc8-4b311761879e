package com.hg.engine.infrastructure.persistence.convertor;

import com.hg.engine.domain.tp.model.System;
import com.hg.engine.infrastructure.persistence.po.SystemPO;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-11T22:50:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.2 (Oracle Corporation)"
)
@Component
public class SystemConverterImpl implements SystemConverter {

    @Override
    public System po2domain(SystemPO systemPo) {
        if ( systemPo == null ) {
            return null;
        }

        System system = new System();

        system.setId( systemPo.getId() );
        system.setName( systemPo.getName() );
        Map<String, Object> map = systemPo.getProfile();
        if ( map != null ) {
            system.setProfile( new LinkedHashMap<String, Object>( map ) );
        }
        Map<String, Object> map1 = systemPo.getVariables();
        if ( map1 != null ) {
            system.setVariables( new LinkedHashMap<String, Object>( map1 ) );
        }
        system.setSvgContent( systemPo.getSvgContent() );
        system.setType( systemPo.getType() );

        return system;
    }

    @Override
    public SystemPO domain2po(System system) {
        if ( system == null ) {
            return null;
        }

        SystemPO systemPO = new SystemPO();

        systemPO.setId( system.getId() );
        systemPO.setName( system.getName() );
        Map<String, Object> map = system.getProfile();
        if ( map != null ) {
            systemPO.setProfile( new LinkedHashMap<String, Object>( map ) );
        }
        Map<String, Object> map1 = system.getVariables();
        if ( map1 != null ) {
            systemPO.setVariables( new LinkedHashMap<String, Object>( map1 ) );
        }
        systemPO.setSvgContent( system.getSvgContent() );
        systemPO.setType( system.getType() );

        return systemPO;
    }
}
