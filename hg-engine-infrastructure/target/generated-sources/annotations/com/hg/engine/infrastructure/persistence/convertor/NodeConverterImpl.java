package com.hg.engine.infrastructure.persistence.convertor;

import com.hg.engine.domain.tp.model.Pipe;
import com.hg.engine.domain.tp.model.Valve;
import com.hg.engine.infrastructure.persistence.po.PipePO;
import com.hg.engine.infrastructure.persistence.po.ValvePO;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-11T22:50:13+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 17.0.2 (Oracle Corporation)"
)
@Component
public class NodeConverterImpl extends NodeConverter {

    @Override
    public ValvePO valve2po(Valve valve) {
        if ( valve == null ) {
            return null;
        }

        ValvePO valvePO = new ValvePO();

        valvePO.setId( valve.getId() );
        valvePO.setViewId( valve.getViewId() );
        valvePO.setType( valve.getType() );
        valvePO.setName( valve.getName() );
        valvePO.setSystemId( valve.getSystemId() );
        valvePO.setNextNode( domain2poList( valve.getNextNode() ) );
        valvePO.setProperty( valve.getProperty() );
        valvePO.setValveType( valve.getValveType() );
        valvePO.setOpen( valve.getOpen() );
        List<String> list1 = valve.getOpenMode();
        if ( list1 != null ) {
            valvePO.setOpenMode( new ArrayList<String>( list1 ) );
        }

        return valvePO;
    }

    @Override
    public Valve po2valve(ValvePO po) {
        if ( po == null ) {
            return null;
        }

        Valve valve = new Valve();

        valve.setId( po.getId() );
        valve.setType( po.getType() );
        valve.setViewId( po.getViewId() );
        valve.setName( po.getName() );
        valve.setSystemId( po.getSystemId() );
        valve.setNextNode( po2domainList( po.getNextNode() ) );
        valve.setProperty( po.getProperty() );
        valve.setValveType( po.getValveType() );
        valve.setOpen( po.getOpen() );
        List<String> list1 = po.getOpenMode();
        if ( list1 != null ) {
            valve.setOpenMode( new ArrayList<String>( list1 ) );
        }

        return valve;
    }

    @Override
    public PipePO pipe2po(Pipe pipe) {
        if ( pipe == null ) {
            return null;
        }

        PipePO pipePO = new PipePO();

        pipePO.setId( pipe.getId() );
        pipePO.setViewId( pipe.getViewId() );
        pipePO.setType( pipe.getType() );
        pipePO.setName( pipe.getName() );
        pipePO.setSystemId( pipe.getSystemId() );
        pipePO.setNextNode( domain2poList( pipe.getNextNode() ) );
        pipePO.setProperty( pipe.getProperty() );
        pipePO.setFlow( pipe.getFlow() );
        pipePO.setFlowDirection( pipe.getFlowDirection() );

        return pipePO;
    }

    @Override
    public Pipe po2pipe(PipePO po) {
        if ( po == null ) {
            return null;
        }

        Pipe pipe = new Pipe();

        pipe.setId( po.getId() );
        pipe.setType( po.getType() );
        pipe.setViewId( po.getViewId() );
        pipe.setName( po.getName() );
        pipe.setSystemId( po.getSystemId() );
        pipe.setNextNode( po2domainList( po.getNextNode() ) );
        pipe.setProperty( po.getProperty() );
        pipe.setFlow( po.getFlow() );
        pipe.setFlowDirection( po.getFlowDirection() );

        return pipe;
    }
}
