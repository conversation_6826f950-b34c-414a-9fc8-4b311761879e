package com.hg.engine.shared.framework.i18n;

import com.hg.engine.shared.common.util.MessageUtil;
import jakarta.annotation.PostConstruct;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Configuration;


@Configuration
public class MessageUtilConfig {


    private final MessageSource messageSource;

    public MessageUtilConfig(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @PostConstruct
    public void init() {
        MessageUtil.setMessageSource(messageSource);
    }
}
