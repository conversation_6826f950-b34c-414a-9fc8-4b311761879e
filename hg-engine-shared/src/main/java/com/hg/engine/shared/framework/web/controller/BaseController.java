package com.hg.engine.shared.framework.web.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hg.engine.shared.common.model.PageResult;
import com.hg.engine.shared.framework.web.util.PagConvertInterface;
import com.hg.engine.shared.framework.web.util.PageUtils;


/**
 * <AUTHOR>
 */
public class BaseController {


    /**
     * i
     * 获取分页返回数据
     *
     * @return 分页对象
     */
    protected <T> Page<T> startPage() {
        return PageUtils.startPage();
    }


    /**
     * 获取分页返回数据
     *
     * @param list 查询列表
     * @param <T>  查询对象
     * @return 分页对象
     */
    protected <T> PageResult<T> getPage(Page<T> list) {
        return PageUtils.getPage(list);
    }

    public static <V, T> PageResult<V> getPage(Page<T> page, PagConvertInterface<V, T> records) {
        return PageUtils.getPage(page, records);
    }





}
