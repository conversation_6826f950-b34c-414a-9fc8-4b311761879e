package com.hg.engine.shared.framework.redis;


import com.alibaba.fastjson2.filter.ValueFilter;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * LocalDateTime 格式转换
 *
 * <AUTHOR>
 */
public class LocalDateTimeValueFilter implements ValueFilter {

    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_DATE_TIME_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT_PATTERN = "yyyy-MM-dd";
    /**
     * 默认时间格式
     */
    public static final String DEFAULT_TIME_FORMAT_PATTERN = "HH:mm:ss";

    /**
     * 默认日期时间格式
     */
    public static final DateTimeFormatter DEFAULT_DATE_TIME_FORMAT = DateTimeFormatter.ofPattern(DEFAULT_DATE_TIME_FORMAT_PATTERN);
    /**
     * 默认日期格式
     */
    public static final DateTimeFormatter DEFAULT_DATE_FORMAT = DateTimeFormatter.ofPattern(DEFAULT_DATE_FORMAT_PATTERN);
    /**
     * 默认时间格式
     */
    public static final DateTimeFormatter DEFAULT_TIME_FORMAT = DateTimeFormatter.ofPattern(DEFAULT_TIME_FORMAT_PATTERN);


    @Override
    public Object apply(Object object, String name, Object value) {
        if (value instanceof LocalDateTime) {
            value = DEFAULT_DATE_TIME_FORMAT.format(((LocalDateTime) value));
        } else if (value instanceof LocalDate) {
            value = DEFAULT_DATE_FORMAT.format(((LocalDate) value));
        } else if (value instanceof LocalTime) {
            value = DEFAULT_TIME_FORMAT.format(((LocalTime) value));
        }
        return value;
    }
}
