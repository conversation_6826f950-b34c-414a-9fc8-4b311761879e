package com.hg.engine.shared.framework.web.handler;

import com.hg.engine.shared.common.constant.HttpStatus;
import com.hg.engine.shared.common.model.ResultBody;
import com.hg.engine.shared.exception.ServeException;
import com.hg.engine.shared.framework.web.util.ResultUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.BindException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.nio.file.AccessDeniedException;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    /**
     * 权限校验异常
     */
    @ExceptionHandler(AccessDeniedException.class)
    public ResultBody<?> handleAccessDeniedException(AccessDeniedException e, HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',权限校验失败'{}'", requestURI, e.getMessage());
        return ResultUtil.error(HttpStatus.FORBIDDEN, "没有权限，请联系管理员授");
    }

    /**
     * 请求方式不支
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResultBody<?> handleHttpRequestMethodNotSupported(HttpRequestMethodNotSupportedException e,
                                                             HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        log.error("请求地址'{}',不支{}'请求", requestURI, e.getMethod());
        return ResultUtil.error(e.getMessage());
    }

    /**
     * 业务异常
     */
    @ExceptionHandler(ServeException.class)
    public ResultBody<?> handleServiceException(ServeException e) {
        log.error(e.getMessage(), e);
        Integer code = e.getCode();
        return null != code ? ResultUtil.error(code, e.getMessage()) : ResultUtil.error(e.getMessage());
    }

    /**
     * 拦截未知的运行时异常
     */
    @ExceptionHandler(RuntimeException.class)
    public ResultBody<?> handleRuntimeException(RuntimeException e, HttpServletRequest request) {
        log.error("请求地址'{}',发生未知异常.", request.getRequestURI(), e);
        return ResultUtil.error(e.getMessage());
    }

    /**
     * 系统异常
     */
    @ExceptionHandler(Exception.class)
    public ResultBody<?> handleException(Exception e, HttpServletRequest request) {
        log.error("请求地址'{}',发生系统异常.", request.getRequestURI(), e);
        return ResultUtil.error(e.getMessage());
    }

    /**
     * 自定义验证异
     */
    @ExceptionHandler(BindException.class)
    public ResultBody<?> handleBindException(BindException e) {
        log.error(e.getMessage(), e);
        String message = e.getAllErrors().get(0).getDefaultMessage();
        return ResultUtil.error(message);
    }

    /**
     * 自定义验证异
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Object handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        log.error(e.getMessage(), e);
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return ResultUtil.error(HttpStatus.REQUEST_VALIDATION_ERR, message);
    }

}
