package com.hg.engine.shared.exception.base;

import com.hg.engine.shared.common.util.MessageUtil;
import com.hg.engine.shared.common.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 基础异常
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BaseException extends RuntimeException {
    static final long serialVersionUID = 1L;

    /**
     * 错误
     */
    Integer code;


    /**
     * 消息Key I18n配置文件
     */
    String msgKey;

    /**
     * 默认消息
     */
    String defMsg;


    public BaseException(Integer code, String msgKey, String defMsg) {
        super(StrUtil.isNotEmpty(MessageUtil.get(msgKey)) ? MessageUtil.get(msgKey) : defMsg);
        this.code = code;
        this.msgKey = msgKey;
        this.defMsg = defMsg;
    }


}

