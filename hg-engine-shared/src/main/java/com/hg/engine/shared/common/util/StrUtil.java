package com.hg.engine.shared.common.util;

/**
 * <AUTHOR>
 */
public class StrUtil extends cn.hutool.core.util.StrUtil {
    public static final String SEPARATOR = ",";

    public static String replaceEach(String asc, String[] strings, String[] replaces) {
        String str = asc;
        for (String string : strings) {
            for (String replace : replaces) {
                str = str.replace(string, replace);
            }
        }
        return str;
    }
}
