package com.hg.engine.shared.common.annotation;

import java.lang.annotation.*;

/**
 * 日志注解
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {
    
    /**
     * 操作描述
     */
    String value() default "";
    
    /**
     * 操作类型
     */
    String type() default "";
    
    /**
     * 是否记录请求参数
     */
    boolean requestParam() default true;
    
    /**
     * 是否记录响应结果
     */
    boolean responseResult() default false;
}
