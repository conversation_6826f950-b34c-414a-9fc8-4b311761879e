package com.hg.engine.shared.framework.web.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * LocalDateTime JSON反序列化工具
 * <AUTHOR>
 */
public class LocalDateTimeDeserializer extends JsonDeserializer<LocalDateTime> implements BaseTimeDeserializer {

    @Override
    public LocalDateTime deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        return this.getLocalDateTimeBySource(parser.getValueAsString());
    }
}
