package com.hg.engine.shared.framework.redis;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.lang.Nullable;
/**
 * redis cache 扩展cache name自动化配置
 *
 * <AUTHOR>
 */
public class RedisAutoCacheManager extends RedisCacheManager {

    RedisAutoCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultCacheConfiguration) {
        super(cacheWriter, defaultCacheConfiguration);
    }

    /**
     * 重写创建redis缓存key策略
     * 后续可以根据name匹配各种规则
     *
     * @param name
     * @param cacheConfig
     * @return
     */
    @Override
    protected RedisCache createRedisCache(String name, @Nullable RedisCacheConfiguration cacheConfig) {
        return super.createRedisCache(name, cacheConfig);
    }

}
