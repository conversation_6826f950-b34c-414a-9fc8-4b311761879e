package com.hg.engine.shared.common.model;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 分页返回体
 *
 * <AUTHOR>
 */
@Data
@ToString
public class PageResult<T> {
    /**
     * 当前页
     */
    private long pageNum;
    /**
     * 每页的数量
     */
    private long pageSize;
    /**
     * 当前页的数量
     */
    private long size;

    /**
     * 总页数
     */
    private long pages;

    private long total;

    private List<T> rows;
}
