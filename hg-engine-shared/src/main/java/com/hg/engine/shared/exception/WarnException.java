package com.hg.engine.shared.exception;


import com.hg.engine.shared.common.constant.HttpStatus;
import com.hg.engine.shared.exception.base.BaseException;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public final class WarnException extends BaseException {

    public WarnException(String msgKey, String defMsg) {
        super(HttpStatus.WARN, msgKey, defMsg);
    }

    public WarnException(String msgKey) {
        super(HttpStatus.WARN, msgKey, "警告异常，请配置消息");
    }
}
