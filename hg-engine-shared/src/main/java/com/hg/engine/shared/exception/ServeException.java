package com.hg.engine.shared.exception;


import com.hg.engine.shared.common.constant.HttpStatus;
import com.hg.engine.shared.exception.base.BaseException;

/**
 * 业务异常
 *
 * <AUTHOR>
 */
public class ServeException extends BaseException {


    public ServeException(String msgKey, String defMsg) {
        super(HttpStatus.ERROR, msgKey, defMsg);
    }

    public ServeException(String msg) {
        super(HttpStatus.ERROR, msg, null);
    }

    public ServeException() {
        super(HttpStatus.ERROR, null, "服务异常");
    }

}
