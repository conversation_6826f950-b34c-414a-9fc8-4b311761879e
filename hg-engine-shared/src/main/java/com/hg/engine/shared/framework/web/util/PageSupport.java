package com.hg.engine.shared.framework.web.util;


import com.hg.engine.shared.common.model.PageParam;
import com.hg.engine.shared.common.util.NumberUtil;
import com.hg.engine.shared.common.util.StrUtil;

/**
 * 表格数据处理
 *
 * <AUTHOR>
 */
public class PageSupport {
    /**
     * 当前记录起始索引
     */
    public final static String PAGE_NUM = "pageNum";

    /**
     * 每页显示记录
     */
    public final static String PAGE_SIZE = "pageSize";

    public final static String ASC = "asc";
    public final static String ORDER_BY_COLUMN = "orderByColumn";


    /**
     * 封装分页对象
     */
    public static PageParam getPageParam() {
        PageParam page = new PageParam();
        page.setPageNum(NumberUtil.parseInt(ServletUtils.getParameter(PAGE_NUM), 1));
        page.setPageSize(NumberUtil.parseInt(ServletUtils.getParameter(PAGE_SIZE), 10));
        String asc = ServletUtils.getParameter(ASC);
        String orderBy = ServletUtils.getParameter(ORDER_BY_COLUMN);
        if (StrUtil.isNotEmpty(orderBy)) {
            page.setOrderByColumn(orderBy);
            page.setAsc(asc);
        }
        return page;
    }


}
