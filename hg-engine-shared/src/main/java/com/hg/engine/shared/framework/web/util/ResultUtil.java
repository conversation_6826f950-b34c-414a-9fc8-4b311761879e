package com.hg.engine.shared.framework.web.util;

import com.alibaba.fastjson2.JSON;
import com.hg.engine.shared.common.constant.HttpStatus;
import com.hg.engine.shared.common.model.ResultBody;

/**
 * <AUTHOR>
 */
public class ResultUtil {

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static ResultBody<?> success() {
        return ResultUtil.success("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static ResultBody<?> success(Object data) {
        return success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return 成功消息
     */
    public static ResultBody<?> success(String msg) {
        return success(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static ResultBody<?> success(String msg, Object data) {
        return new ResultBody<>(HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static ResultBody<?> success(Integer code) {
        return new ResultBody<>(code, "操作成功", null);
    }

    /**
     * 返回成功消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static ResultBody<?> success(Integer code,String msg, Object data) {
        return new ResultBody<>(code, msg, data);
    }

    /**
     * 返回警告消息
     *
     * @param msg 返回内容
     * @return 警告消息
     */
    public static ResultBody<?> warn(String msg) {
        return warn(msg, null);
    }

    /**
     * 返回警告消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 警告消息
     */
    public static ResultBody<?> warn(String msg, Object data) {
        return new ResultBody<>(HttpStatus.WARN, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @return 错误消息
     */
    public static ResultBody<?> error() {
        return error("操作失败");
    }

    /**
     * 返回错误消息
     *
     * @param msg 返回内容
     * @return 错误消息
     */
    public static ResultBody<?> error(String msg) {
        return error(msg, null);
    }

    /**
     * 返回错误消息
     *
     * @param msg  返回内容
     * @param data 数据对象
     * @return 错误消息
     */
    public static ResultBody<?> error(String msg, Object data) {
        return new ResultBody<>(HttpStatus.ERROR, msg, data);
    }

    /**
     * 返回错误消息
     *
     * @param code 状态码
     * @param msg  返回内容
     * @return 错误消息
     */
    public static ResultBody<?> error(int code, String msg) {
        return new ResultBody<>(code, msg, null);
    }

    public static String fromJson(ResultBody<?> resultBody) {
        return JSON.toJSONString(resultBody);
    }
}
