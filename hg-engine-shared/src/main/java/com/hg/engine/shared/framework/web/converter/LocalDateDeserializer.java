package com.hg.engine.shared.framework.web.converter;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
public class LocalDateDeserializer extends JsonDeserializer<LocalDate> implements BaseTimeDeserializer {

    @Override
    public LocalDate deserialize(JsonParser parser, DeserializationContext deserializationContext) throws IOException {
        LocalDateTime localDateTime = this.getLocalDateTimeBySource(parser.getValueAsString());
        return localDateTime == null ? null : localDateTime.toLocalDate();
    }
}
