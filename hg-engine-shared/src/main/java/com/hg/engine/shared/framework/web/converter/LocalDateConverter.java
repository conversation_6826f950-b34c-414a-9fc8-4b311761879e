package com.hg.engine.shared.framework.web.converter;

import jakarta.validation.constraints.NotNull;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Configuration
public class LocalDateConverter implements Converter<String, LocalDate>, BaseTimeDeserializer {
    @Override
    public LocalDate convert(@NotNull String source) {
        // 日期格式转转
        LocalDateTime localDateTime = this.getLocalDateTimeBySource(source);
        return localDateTime == null ? null : localDateTime.toLocalDate();
    }
}
