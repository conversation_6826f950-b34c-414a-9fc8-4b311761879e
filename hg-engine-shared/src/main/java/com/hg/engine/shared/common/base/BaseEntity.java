package com.hg.engine.shared.common.base;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 基础实体
 * 所有数据库映射对象都应该继承此
 * 仅存在于基础设施层，不应被领域层引用
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public abstract class BaseEntity implements Serializable {


    /**
     * 创建者
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    protected String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    protected LocalDateTime createTime;

    /**
     * 更新者
     */
    @JsonIgnore
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE, select = false)
    protected String updateBy;

    /**
     * 更新时间
     */
    @JsonIgnore
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE, select = false)
    protected LocalDateTime updateTime;



    public final static String COL_CREATE_BY = "createBy";

    public final static String COL_UPDATE_BY = "updateBy";

    public final static String COL_CREATE_TIME = "createTime";

    public final static String COL_UPDATE_TIME = "updateTime";

    public final static String COL_DELETE = "delete";


}
