package com.hg.engine.shared.common.util;

import java.util.*;
import java.util.stream.Collectors;

public interface TreeUtil {

    /**
     * 节点树抽象类
     *
     * @param <K> 节点ID的数据类
     * @param <T> 节点的数据类
     */
    interface Node<K, T extends Node<K, T>> {

        /**
         * 设置当前节点的ID
         */
        void setId(K id);

        /**
         * 获取当前节点的ID
         *
         * @return 当前节点的ID
         */
        K getId();

        /**
         * 设置当前节点的父节点ID
         */
        void setParentId(K pid);

        /**
         * 获取当前节点的父节点ID
         *
         * @return 当前节点的父节点ID
         */
        K getParentId();

        /**
         * 设置当前节点所拥有的的子节点数
         */
        void setCount(int count);

        /**
         * 获取当前节点所拥有的的子节点数
         *
         * @return 当前节点所拥有的的子节点数
         */
        int getCount();

        /**
         * 设置当前节点的子节点集合
         *
         * @param children 待设置的子节点集
         */
        void setChildren(List<T> children);

        /**
         * 获取当前节点的子节点集合
         *
         * @return 当前节点的子节点集合
         */
        List<T> getChildren();

    }

    /**
     * 构建节点
     *
     * @param src  构建树的源集合（包含所有的子父级节点）
     * @param root 待填充子节点的根节点
     */
    static <I, E extends Node<I, E>> void buildTree(List<E> src, Node<I, E> root) {
        List<E> child = new ArrayList<>();
        src.removeIf(item -> {
            if (null == item.getParentId() || item.getParentId().equals(root.getId())) {
                child.add(item);
                return true;
            }
            return false;
        });
        if (!child.isEmpty()) {
            root.setChildren(child);
            root.setCount(child.size());
            child.forEach(item -> buildTree(src, item));
        }
    }


    /**
     * 构建节点树的简单方
     *
     * @param src   待组装的节点集合
     * @param clazz 节点Class 对象
     * @param rid   节点树中顶级节点的ID
     * @return 组装好的节点
     */
    static <M, N extends Node<M, N>> List<N> build(List<N> src, Class<N> clazz, M rid) {
        Node<M, N> root;
        try {
            root = clazz.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
        root.setId(rid);
        buildTree(src, root);
        return root.getChildren();
    }

    /**
     * 构建节点树的简单方
     *
     * @param src   待组装的节点集合
     * @param clazz 节点Class 对象
     * @return 组装好的节点
     */
    static <M, N extends Node<M, N>> List<N> build(List<N> src, Class<N> clazz) {
        return build(src, clazz, null);
    }


    /**
     * 非递归方式构建节点
     * 注：不要多线程调用此方法，因为不论setChild方法还是LinkedList的add方法都不保证同步
     *
     * @param src  构建树的源集合（包含所有的子父级节点）
     * @param root 待填充子节点的根节点
     */
    static <I, E extends Node<I, E>> List<E> buildTreeNoRecursion(List<E> src, I root) {
        if (root == null) {
            throw new IllegalArgumentException("根节点ID不能为空");
        }
        List<E> result = new LinkedList<>();
        Map<I, E> map = src.stream()
                .collect(Collectors.toMap(E::getId, e -> e));
        for (E elem : src) {
            if (Objects.equals(root, elem.getParentId())) {
                result.add(elem);
            } else {
                E e = map.get(elem.getParentId());
                if (e == null) {
                    continue;
                }
                List<E> child = e.getChildren();
                if (child == null) {
                    LinkedList<E> list = new LinkedList<>();
                    // 这里不保证多线程的同步性，如需要可自己处理
                    list.add(elem);
                    e.setChildren(list);
                } else {
                    e.getChildren().add(elem);
                }
            }
        }
        return result;
    }
}
