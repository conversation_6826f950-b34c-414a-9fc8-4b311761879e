package com.hg.engine.shared.framework.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.hg.engine.shared.common.base.BaseEntity;
import com.hg.engine.shared.common.util.SecurityUtil;
import com.hg.engine.shared.common.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class MybatisPlusMetaObjectHandler implements MetaObjectHandler {

    SecurityUtil securityUtil;



    @Override
    public void insertFill(MetaObject metaObject) {
        if (metaObject.getOriginalObject() instanceof BaseEntity) {
            Object createTime = this.getFieldValByName(BaseEntity.COL_CREATE_TIME, metaObject);
            if (createTime == null) {
                this.setFieldValByName(BaseEntity.COL_CREATE_TIME, LocalDateTime.now(), metaObject);
            }
            Object updateTime = this.getFieldValByName(BaseEntity.COL_UPDATE_TIME, metaObject);
            if (updateTime == null) {
                this.setFieldValByName(BaseEntity.COL_UPDATE_TIME, LocalDateTime.now(), metaObject);
            }
            String userId = securityUtil.getUserId();
            Object createBy = this.getFieldValByName(BaseEntity.COL_CREATE_BY, metaObject);
            if (null == createBy && StrUtil.isNotEmpty(userId)) {
                this.setFieldValByName(BaseEntity.COL_CREATE_BY, userId, metaObject);
            }
            Object updateBy = this.getFieldValByName(BaseEntity.COL_UPDATE_BY, metaObject);
            if (null == updateBy && StrUtil.isNotEmpty(userId)) {
                this.setFieldValByName(BaseEntity.COL_UPDATE_BY, userId, metaObject);
            }
        }
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if (metaObject.getOriginalObject() instanceof BaseEntity) {
            Object updateTime = this.getFieldValByName(BaseEntity.COL_UPDATE_TIME, metaObject);
            if (updateTime == null) {
                this.setFieldValByName(BaseEntity.COL_UPDATE_TIME, LocalDateTime.now(), metaObject);
            }
            String userId = securityUtil.getUserId();
            Object updateBy = this.getFieldValByName(BaseEntity.COL_UPDATE_BY, metaObject);
            if (null == updateBy && StrUtil.isNotEmpty(userId)) {
                this.setFieldValByName(BaseEntity.COL_UPDATE_BY, userId, metaObject);
            }
        }
    }
}
