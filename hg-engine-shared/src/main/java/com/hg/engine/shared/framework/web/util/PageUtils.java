package com.hg.engine.shared.framework.web.util;

import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hg.engine.shared.common.model.PageParam;
import com.hg.engine.shared.common.model.PageResult;
import com.hg.engine.shared.common.util.SqlUtil;
import com.hg.engine.shared.common.util.StrUtil;
import com.hg.engine.shared.exception.ServeException;


import java.util.ArrayList;
import java.util.List;

/**
 * 分页工具
 *
 * <AUTHOR>
 */
public class PageUtils {
    /**
     * 设置请求分页数据
     */
    public static <T> Page<T> startPage() {
        PageParam p = PageSupport.getPageParam();
        Integer pageNum = p.getPageNum();
        Integer pageSize = p.getPageSize();
        Page<T> page = Page.of(pageNum, pageSize);
        List<OrderItem> orderItems = buildOrderItem(p.getAsc(), p.getOrderByColumn());
        if (CollectionUtils.isNotEmpty(orderItems)) {
            page.setOrders(orderItems);
        }
        return page;
    }

    /**
     * 构建排序
     * <p>
     * 支持的用法如
     * {isAsc:"asc",orderByColumn:"id"} order by id asc
     * {isAsc:"asc",orderByColumn:"id,createTime"} order by id asc,create_time asc
     * {isAsc:"desc",orderByColumn:"id,createTime"} order by id desc,create_time desc
     * {isAsc:"asc,desc",orderByColumn:"id,createTime"} order by id asc,create_time desc
     */
    private static List<OrderItem> buildOrderItem(String asc, String orderByColumn) {
        if (StrUtil.isBlank(orderByColumn) || StrUtil.isBlank(asc)) {
            return null;
        }
        String orderBy = SqlUtil.escapeOrderBySql(orderByColumn);
        orderBy = StrUtil.toUnderlineCase(orderBy);
        // 兼容前端排序类型
        //替换其中"ascending", "descending" "asc", "desc"
        asc = StrUtil.replaceEach(asc, new String[]{"ascending", "descending"}, new String[]{"asc", "desc"});
        // StringUtils.SEPARATOR =  ","
        String[] orderByArr = orderBy.split(StrUtil.SEPARATOR);
        String[] isAscArr = asc.split(StrUtil.SEPARATOR);
        if (isAscArr.length != 1 && isAscArr.length != orderByArr.length) {
            throw new ServeException("排序参数有误");
        }
        List<OrderItem> list = new ArrayList<>();
        // 每个字段各自排序
        for (int i = 0; i < orderByArr.length; i++) {
            String orderByStr = orderByArr[i];
            String isAscStr = isAscArr.length == 1 ? isAscArr[0] : isAscArr[i];
            if ("asc".equals(isAscStr)) {
                list.add(OrderItem.asc(orderByStr));
            } else if ("desc".equals(isAscStr)) {
                list.add(OrderItem.desc(orderByStr));
            } else {
                throw new ServeException("排序参数有误");
            }
        }
        return list;
    }

    /**
     * 获取分页返回数据
     *
     * @param <T> 查询对象
     * @return 分页对象
     */
    public static <T> PageResult<T> getPage(Page<T> page) {
        PageResult<T> result = new PageResult<>();
        List<T> records = page.getRecords();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setSize(null == page.maxLimit() ? 0L : page.maxLimit());
        result.setTotal(page.getTotal());
        result.setPages(page.getPages());
        result.setRows(records);
        return result;
    }

    public static <V, T> PageResult<V> getPage(Page<T> page, PagConvertInterface<V, T> records) {
        PageResult<V> result = new PageResult<>();
        result.setPageNum(page.getCurrent());
        result.setPageSize(page.getSize());
        result.setSize(null == page.maxLimit() ? 0L : page.maxLimit());
        result.setTotal(page.getTotal());
        result.setPages(page.getPages());
        if (null != records) {
            List<T> t = page.getRecords();
            List<V> convert = records.convert(t);
            result.setRows(convert);
        }
        return result;
    }

}
