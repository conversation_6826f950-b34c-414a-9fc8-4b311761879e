package com.hg.engine.shared.common.util;

import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 获取i18n资源文件
 *
 * <AUTHOR>
 */
public class MessageUtil {


    private static MessageSource MESSAGE_SOURCE;

    /**
     * 需要注
     * @param source message
     */
    public static void setMessageSource(MessageSource source) {
        MESSAGE_SOURCE = source;
    }

    /**
     * 获取国际化信
     *
     * @param key  资源文件中的 key
     * @param args 可选参数，用于占位符替
     * @return 国际化后的字符串
     */
    public static String get(String key, Object... args) {
        Locale locale = LocaleContextHolder.getLocale();
        return MESSAGE_SOURCE.getMessage(key, args, locale);
    }

    public static String getWithLocale(String key, Locale locale, Object... args) {
        return MESSAGE_SOURCE.getMessage(key, args, locale);
    }
}
