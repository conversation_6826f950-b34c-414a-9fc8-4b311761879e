package com.hg.engine.shared.framework.web.converter;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.regex.Pattern;

public interface BaseTimeDeserializer {

    Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2}$");
    Pattern DATETIME_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}$");

    /**
     * 时间转换
     *
     * @param source 时间字符
     * @return 时间
     */
    default LocalDateTime getLocalDateTimeBySource(String source) {
        if (source == null || source.trim().length() == 0) {
            return null;
        }
        source = source.trim();

        if (!DATE_PATTERN.matcher(source).matches() && !DATETIME_PATTERN.matcher(source).matches()) {
            throw new RuntimeException("日期参数格式错误: " + source);
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss", Locale.CHINA);
        if (DATE_PATTERN.matcher(source).matches()) {
            source += " 00:00:00";
        }
        return LocalDateTime.parse(source, formatter);
    }

}
