package com.hg.engine.shared.framework.web.handler;


import com.alibaba.fastjson2.JSON;
import com.hg.engine.shared.common.model.ResultBody;
import com.hg.engine.shared.framework.web.annotation.IgnoreResultBodyConvert;
import com.hg.engine.shared.framework.web.annotation.ResultBodyConvert;
import com.hg.engine.shared.framework.web.util.ResultUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * <AUTHOR>
 */
@RestControllerAdvice
public class GlobalResponseResultHandler implements ResponseBodyAdvice<Object> {

    @Override
    public boolean supports(MethodParameter methodParameter, Class aClass) {
        IgnoreResultBodyConvert noResultBodyConvert = methodParameter.getMethodAnnotation(IgnoreResultBodyConvert.class);
        if (null != noResultBodyConvert) {
            return false;
        }
        ResultBodyConvert annotation = methodParameter.getContainingClass().getAnnotation(ResultBodyConvert.class);
        if (annotation != null) {
            return true;
        }
        ResultBodyConvert methodAnnotation = methodParameter.getMethodAnnotation(ResultBodyConvert.class);
        return methodAnnotation != null;
    }

    @Override
    public Object beforeBodyWrite(Object o, MethodParameter methodParameter, MediaType mediaType, Class aClass, ServerHttpRequest request, ServerHttpResponse serverHttpResponse) {
        ResultBodyConvert annotation = methodParameter.getContainingClass().getAnnotation(ResultBodyConvert.class);
        int code = annotation.succeedCode();
        if (o == null) {
            return ResultUtil.success(code);
        }
        if (o instanceof ResultBody) {
            return o;
        }
        if (o instanceof String) {
            ResultBody<?> success = ResultUtil.success(code, "操作成功", o.toString());

            return JSON.toJSONString(success);
        }
        return ResultUtil.success(code, "操作成功", o);
    }
}
