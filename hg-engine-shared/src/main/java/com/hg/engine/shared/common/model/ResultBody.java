package com.hg.engine.shared.common.model;

import com.alibaba.fastjson2.JSON;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 返回结果封装
 *
 * <AUTHOR>
 */
@Getter
@Setter
@Data
public class ResultBody<T> {
    /**
     * 状态
     */
    private Integer code;

    /**
     * 消息
     */
    private String message;

    /**
     * 回执
     */
    private T data;

    public ResultBody(Integer code) {
        this.code = code;
    }

    public ResultBody(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResultBody(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }

}
