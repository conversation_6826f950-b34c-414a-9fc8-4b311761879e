package com.hg.engine.shared.framework.redis.lock;


import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.NamedThreadLocal;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import java.lang.reflect.Method;
import java.util.UUID;


/**
 * <AUTHOR>
 */
@Aspect
@Slf4j
@AllArgsConstructor
public class RedisLockAspect {

    RedisLockService redisService;


    private final ExpressionParser parser = new SpelExpressionParser();

    private final DefaultParameterNameDiscoverer nameDiscoverer = new DefaultParameterNameDiscoverer();


    private static final ThreadLocal<String> REDIS_LOCK = new NamedThreadLocal<>("redis.lock");

    private final static String KEY_PREFIX = "redisLock:";

    /**
     * 环绕通知  加锁 解锁
     *
     * @param joinPoint joinPoint
     * @return Object
     */
    @Around(value = "@annotation(RedisLock)")
    public Object redisLockAop(ProceedingJoinPoint joinPoint) {
        RedisLock lock = ((MethodSignature) joinPoint.getSignature()).getMethod().getAnnotation(RedisLock.class);
        String uuid = UUID.randomUUID().toString();
        String key = getKey(joinPoint, lock.key());
        log.info(Thread.currentThread().getName() + ":获取key:" + key);
        if (REDIS_LOCK.get() != null) {
            //当前线程已经获取到锁 不需要重复获取锁。保证可重入性
            return execute(key, uuid, joinPoint);
        }
        if (redisService.tryLock(key, uuid, lock.expireTime(), lock.timeout(), lock.interval())) {
            REDIS_LOCK.set(key);
            return execute(key, uuid, joinPoint);
        }
        throw new RedisLockException();
    }

    private Object execute(String key, String uuid, ProceedingJoinPoint joinPoint) {
        //获取到锁进行标记 执行方法
        try {
            return joinPoint.proceed();
        } catch (Throwable throwable) {
            log.error("redisAop方法执行异常:{}", throwable.toString());
        } finally {
            //方法执行结束 释放锁
            REDIS_LOCK.remove();
            redisService.unLock(key, uuid);
        }
        return null;
    }


    /**
     * 根据参数 和注解 获取 redis key值
     *
     * @param joinPoint joinPoint
     * @return spel
     */
    public String getKey(ProceedingJoinPoint joinPoint, String spelKey) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        Object[] args = joinPoint.getArgs();
        return KEY_PREFIX + ":" + generateKey(spelKey, method, args);
    }


    private String generateKey(String spelKey, Method method, Object[] args) {
        if (spelKey == null || spelKey.isEmpty()) {
            return method.getDeclaringClass().getName() + "." + method.getName();
        }
        // 支持 SpEL 表达式
        MethodBasedEvaluationContext context = new MethodBasedEvaluationContext(null, method, args, nameDiscoverer);
        return parser.parseExpression(spelKey).getValue(context, String.class);
    }

}
