package com.hg.engine.shared.framework.redis.lock;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class RedisLockServiceImpl implements RedisLockService {

    RedisTemplate<String, Object> redisTemplate;


    @Override
    public boolean tryLock(String key, String value, long expireTime, long timeout, long interval) {
        if (interval <= 0) {
            //默认等待时间 30 毫秒
            interval = 30L;
        }
        if (timeout > 0) {
            long begin = System.currentTimeMillis();
            while (System.currentTimeMillis() - begin < timeout) {
                if (Boolean.TRUE.equals(redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.MILLISECONDS))) {
                    log.info(Thread.currentThread().getName() + ":" + key + ":上锁");
                    return true;
                }
                //等待
                synchronized (Thread.currentThread()) {
                    log.info(Thread.currentThread().getName() + ":等待");
                    try {
                        Thread.currentThread().wait(interval);
                    } catch (InterruptedException e) {
                        log.error("redis锁等待异常", e);
                    }
                }
            }
            return false;
        } else {
            Boolean is = redisTemplate.opsForValue().setIfAbsent(key, value, expireTime, TimeUnit.MILLISECONDS);
            return Boolean.TRUE.equals(is);
        }
    }

    @Override
    public void unLock(String key, String value) {
        Boolean delete = redisTemplate.delete(key);
        if (null != delete && delete) {
            log.info(Thread.currentThread().getName() + ":" + key + ":解锁成功");
        }
    }
}
