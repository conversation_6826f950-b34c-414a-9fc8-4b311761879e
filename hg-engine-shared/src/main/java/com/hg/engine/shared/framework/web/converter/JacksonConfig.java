package com.hg.engine.shared.framework.web.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Configuration
public class JacksonConfig {


    @Autowired
    public void appConfigureObjectMapper(ObjectMapper objectMapper) {
        SimpleModule module = new SimpleModule();

        module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer());
        module.addDeserializer(LocalDate.class, new LocalDateDeserializer());
        module.addSerializer(LocalDateTime.class,new LocalDateTimeSerializer());
        module.addSerializer(LocalDate.class,new LocalDateSerializer());
        objectMapper.registerModule(module);
    }

}
