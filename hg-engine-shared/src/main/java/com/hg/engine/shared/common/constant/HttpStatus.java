package com.hg.engine.shared.common.constant;

/**
 * 返回状态码
 *
 * <AUTHOR>
 */
public class HttpStatus {
    /**
     * 操作成功
     */
    public static final int SUCCESS = 200;

    /**
     * 参数验证异常
     */
    public static final int REQUEST_VALIDATION_ERR = 400;

    /**
     * 未授权
     */
    public static final int UNAUTHORIZED = 401;

    /**
     * 访问受限，授权过期
     */
    public static final int FORBIDDEN = 403;

    /**
     * 资源，服务未找到
     */
    public static final int NOT_FOUND = 404;

    /**
     * 不允许的http方法
     */
    public static final int BAD_METHOD = 405;


    /**
     * 系统内部错误
     */
    public static final int ERROR = 500;


    /**
     * 系统警告消息
     */
    public static final int WARN = 701;


}
