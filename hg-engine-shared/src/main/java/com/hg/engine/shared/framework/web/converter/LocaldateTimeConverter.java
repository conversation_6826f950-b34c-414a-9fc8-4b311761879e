package com.hg.engine.shared.framework.web.converter;


import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Configuration
public class LocaldateTimeConverter implements Converter<String, LocalDateTime>, BaseTimeDeserializer {


    @Override
    public LocalDateTime convert(String source) {
        // 日期格式转转
        return this.getLocalDateTimeBySource(source);
    }
}
