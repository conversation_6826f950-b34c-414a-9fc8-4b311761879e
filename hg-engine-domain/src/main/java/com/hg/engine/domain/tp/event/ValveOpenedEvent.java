package com.hg.engine.domain.tp.event;

import com.hg.engine.domain.tp.model.Valve;

import java.util.List;

/**
 * 阀门打开事件
 * <AUTHOR>
 */
@NodeEventTypeMapping("ValveOpened")
public class ValveOpenedEvent extends NodeEvent {

    /**
     * 阀门开启模式，定义阀门在开启状态下流体的流向
     * 对于三通阀，指定开启时流体通过的管道
     */
    private List<String> openMode;


    public ValveOpenedEvent(String id) {
        super(id);
    }


    public List<String> getOpenMode() {
        return openMode;
    }

    public void setOpenMode(List<String> openMode) {
        this.openMode = openMode;
    }
}