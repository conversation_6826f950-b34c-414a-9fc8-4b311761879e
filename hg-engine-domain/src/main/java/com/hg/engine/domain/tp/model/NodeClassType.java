package com.hg.engine.domain.tp.model;

public enum NodeClassType {
    VALVE("hgValve", Valve.class),
    PIPE("hgPipe", Pipe.class);

    String type;

    Class<? extends Node> clazz;

    NodeClassType(String type, Class<? extends Node> clazz) {
        this.type = type;
        this.clazz = clazz;
    }

    /**
     * 根据type获取对应的class
     *
     * @param type 类型字符串
     * @return 对应的class，未找到返回null
     */
    public static Class<? extends Node> getClassByType(String type) {
        NodeClassType nodeType = fromType(type);
        return nodeType != null ? nodeType.getClazz() : null;
    }

    public static NodeClassType fromType(String type) {
        for (NodeClassType nodeType : NodeClassType.values()) {
            if (nodeType.getType().equals(type)) {
                return nodeType;
            }
        }
        return null;
    }

    public String getType() {
        return type;
    }

    public Class<? extends Node> getClazz() {
        return clazz;
    }
}
