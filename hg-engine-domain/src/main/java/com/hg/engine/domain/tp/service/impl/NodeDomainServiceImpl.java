
package com.hg.engine.domain.tp.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.hg.engine.domain.tp.model.Node;
import com.hg.engine.domain.tp.repository.NodeRepository;
import com.hg.engine.domain.tp.service.NodeDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class NodeDomainServiceImpl implements NodeDomainService {

    public final NodeRepository nodeRepository;

    /**
     * 更新所有节点
     */
    public void updateNodes(List<Node> nodeList, String systemId) {
        if (CollUtil.isEmpty(nodeList)) {
            log.warn("传入的节点列表为空，无法执行批量更新");
            return;
        }
        // 删除所有节点
        nodeRepository.deleteSystemId(systemId);
        // 插入所有节点
        nodeRepository.saveNodes(nodeList);
    }
}
