package com.hg.engine.domain.tp.model;

import lombok.Data;

import java.util.Map;

@Data
public class System {

    /**
     * 系统唯一标识符
     */
    private String id;

    /**
     * 系统名称
     */
    private String name;

    /**
     * 系统界面配置
     * 包含宽度、高度、背景色等界面相关配置
     */
    private Map<String, Object> profile;


    /**
     * 系统变量
     * 存储系统中使用的变量信息
     */
    private Map<String, Object> variables;

    /**
     * SVG内容
     * 系统的SVG图形表示
     */
    private String svgContent;
    private String type;

}
