package com.hg.engine.domain.tp.event;

import com.alibaba.fastjson2.annotation.JSONCreator;
import com.alibaba.fastjson2.annotation.JSONField;
import org.springframework.context.ApplicationEvent;

/**
 * 节点事件基类
 */
public abstract class NodeEvent extends ApplicationEvent {

    private final String id;

    @JSONCreator
    public NodeEvent(@JSONField(name = "id") String id) {
        super(id);
        this.id = id;
    }

    public String getId() {
        return id;
    }
}