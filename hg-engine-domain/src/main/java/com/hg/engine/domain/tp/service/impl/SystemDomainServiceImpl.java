package com.hg.engine.domain.tp.service.impl;

import com.hg.engine.domain.tp.model.System;
import com.hg.engine.domain.tp.repository.SystemRepository;
import com.hg.engine.domain.tp.service.SystemDomainService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class SystemDomainServiceImpl implements SystemDomainService {

    private final SystemRepository systemRepository;

    @Override
    public System getById(String id) {
        return systemRepository.getById(id);
    }

    @Override
    public void updateById(System system) {
        systemRepository.updateById(system);
    }

    @Override
    public void save(System system) {
        systemRepository.save(system);
    }
}
