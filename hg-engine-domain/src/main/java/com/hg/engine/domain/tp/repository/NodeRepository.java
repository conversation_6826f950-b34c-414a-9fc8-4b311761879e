package com.hg.engine.domain.tp.repository;

import com.hg.engine.domain.tp.model.Node;
import org.springframework.stereotype.Service;

import java.util.List;

public interface NodeRepository {
    /**
     * 根据ID查找节点
     * @param id 节点ID
     * @return 节点对象
     */
    Node findById(String id);

    /**
     * 根据系统ID查找节点列表
     * @param systemId 系统ID
     * @return 节点列表
     */
    List<Node> findSystemId(String systemId);

    /**
     * 根据父节点ID查找子节点列表
     * @param parentId 父节点ID
     * @return 子节点列表
     */
    List<Node> findChildrenByParentId(String parentId);

     List<Node> getLastNodeList(String id);

    /**
     * 获取节点,包含上级关系和下级关系
     * @param id  id
     * @return 节点
     */
    Node getNodeWithRelations(String id);

    /**
     * 保存节点
     * @param node 节点对象
     */
    void save(Node node);

    /**
     * 批量保存节点
     * @param nodes 节点列表
     */
    void saveNodes(List<Node> nodes);

    /**
     * 删除节点
     * @param node 节点对象
     */
    void delete(Node node);

    void updateNodes(List<Node> nodeList);



    /**
    * 根据系统id删除所有节点
    * */
    void deleteSystemId(String systemId);

    void updateNode(Node node);

    List<Node> getNextNodeList(String id);

    Node getById(String id);
}
