server:
  port: 8080
  servlet:
    context-path: /api
spring:
  messages:
    basename: messages
    encoding: UTF-8
  profiles:
    active: @profiles.active@
  task:
    scheduling:
      pool:
        size: 2
# springdoc-openapi项目配置
springdoc:
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  api-docs:
    path: /v3/api-docs
  group-configs:
    - group: 'default'
      paths-to-match: '/**'
      packages-to-scan: com.hg.engine.tp.web
  override-with-generic-response: false
# knife4j的增强配置，不需要增强可以不配
knife4j:
  enable: true
  setting:
    language: zh_cn
# MyBatis-Plus 配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 下划线转驼峰
  global-config:
    db-config:
      id-type: ASSIGN_ID
      schema: public
      logic-delete-field: deleted # 逻辑删除字段
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml # XML映射文件位置

# Netty WebSocket配置
netty:
  websocket:
    enabled: true
    port: 8082
    path: /ws
    # 心跳配置
    heartbeat:
      interval: 30000  # 心跳间隔(毫秒)
      timeout: 90000   # 心跳超时(毫秒)
    # 连接配置
    connection:
      max-connections: 10000  # 最大连接数
      idle-timeout: 300000    # 空闲超时(毫秒)
    # 消息配置
    message:
      max-frame-size: 65536   # 最大帧大小(字节)
      max-aggregated-size: 1048576  # 最大聚合大小(字节)
    # 默认订阅主题
    default-topics:
      - system.heartbeat      # 系统心跳
      - node.events          # 节点事件
    # 线程池配置
    thread-pool:
      boss-threads: 1
      worker-threads: 8
      business-threads: 16
