<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
        }
        .panel h3 {
            margin-top: 0;
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 3px;
            margin-bottom: 10px;
        }
        .status.connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }
        .message.sent {
            background-color: #e3f2fd;
        }
        .message.received {
            background-color: #f3e5f5;
        }
        .message.system {
            background-color: #fff3e0;
        }
    </style>
</head>
<body>
    <h1>HG Engine WebSocket 测试页面</h1>
    
    <div class="container">
        <!-- 连接控制面板 -->
        <div class="panel">
            <h3>连接控制</h3>
            <div id="status" class="status disconnected">未连接</div>
            
            <div class="form-group">
                <label for="wsUrl">WebSocket URL:</label>
                <input type="text" id="wsUrl" value="ws://localhost:8082/ws">
            </div>
            
            <div class="form-group">
                <label for="userId">用户ID:</label>
                <input type="text" id="userId" value="test-user-001">
            </div>
            
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
        </div>

        <!-- 消息发送面板 -->
        <div class="panel">
            <h3>发送消息</h3>
            
            <div class="form-group">
                <label for="messageType">消息类型:</label>
                <select id="messageType">
                    <option value="SUBSCRIBE">订阅</option>
                    <option value="UNSUBSCRIBE">取消订阅</option>
                    <option value="PUBLISH">发布</option>
                    <option value="HEARTBEAT">心跳</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="topic">主题:</label>
                <select id="topic">
                    <option value="system.heartbeat">系统心跳</option>
                    <option value="node.events">节点事件</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="payload">消息内容 (JSON):</label>
                <textarea id="payload" rows="4" placeholder='{"message": "测试消息"}'></textarea>
            </div>
            
            <button onclick="sendMessage()" disabled id="sendBtn">发送消息</button>
            <button onclick="sendHeartbeat()" disabled id="heartbeatBtn">发送心跳</button>
        </div>
    </div>

    <!-- 消息日志 -->
    <div class="panel" style="margin-top: 20px;">
        <h3>消息日志</h3>
        <button onclick="clearMessages()">清空日志</button>
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let ws = null;
        let connected = false;

        function connect() {
            const url = document.getElementById('wsUrl').value;
            const userId = document.getElementById('userId').value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    connected = true;
                    updateStatus('已连接', 'connected');
                    document.getElementById('connectBtn').disabled = true;
                    document.getElementById('disconnectBtn').disabled = false;
                    document.getElementById('sendBtn').disabled = false;
                    document.getElementById('heartbeatBtn').disabled = false;
                    
                    addMessage('system', '连接已建立');
                    
                    // 自动订阅默认主题
                    setTimeout(() => {
                        subscribeToDefaultTopics();
                    }, 1000);
                };
                
                ws.onmessage = function(event) {
                    try {
                        const message = JSON.parse(event.data);
                        addMessage('received', `收到消息: ${JSON.stringify(message, null, 2)}`);
                    } catch (e) {
                        addMessage('received', `收到消息: ${event.data}`);
                    }
                };
                
                ws.onclose = function(event) {
                    connected = false;
                    updateStatus('连接已关闭', 'disconnected');
                    document.getElementById('connectBtn').disabled = false;
                    document.getElementById('disconnectBtn').disabled = true;
                    document.getElementById('sendBtn').disabled = true;
                    document.getElementById('heartbeatBtn').disabled = true;
                    
                    addMessage('system', `连接已关闭 (代码: ${event.code})`);
                };
                
                ws.onerror = function(error) {
                    addMessage('system', `连接错误: ${error}`);
                };
                
            } catch (error) {
                addMessage('system', `连接失败: ${error.message}`);
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendMessage() {
            if (!connected || !ws) {
                alert('请先连接WebSocket');
                return;
            }
            
            const messageType = document.getElementById('messageType').value;
            const topic = document.getElementById('topic').value;
            const payloadText = document.getElementById('payload').value;
            
            let payload = null;
            if (payloadText.trim()) {
                try {
                    payload = JSON.parse(payloadText);
                } catch (e) {
                    payload = payloadText;
                }
            }
            
            const message = {
                type: messageType,
                topic: topic,
                payload: payload,
                timestamp: new Date().toISOString()
            };
            
            ws.send(JSON.stringify(message));
            addMessage('sent', `发送消息: ${JSON.stringify(message, null, 2)}`);
        }

        function sendHeartbeat() {
            if (!connected || !ws) {
                alert('请先连接WebSocket');
                return;
            }
            
            const message = {
                type: 'HEARTBEAT',
                timestamp: new Date().toISOString()
            };
            
            ws.send(JSON.stringify(message));
            addMessage('sent', `发送心跳: ${JSON.stringify(message, null, 2)}`);
        }

        function subscribeToDefaultTopics() {
            const defaultTopics = [
                'system.heartbeat',
                'node.events'
            ];
            
            defaultTopics.forEach(topic => {
                const message = {
                    type: 'SUBSCRIBE',
                    topic: topic,
                    timestamp: new Date().toISOString()
                };
                
                ws.send(JSON.stringify(message));
                addMessage('sent', `自动订阅主题: ${topic}`);
            });
        }

        function updateStatus(text, className) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = text;
            statusEl.className = `status ${className}`;
        }

        function addMessage(type, content) {
            const messagesEl = document.getElementById('messages');
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            messageEl.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${content}`;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            // 可以在这里添加自动连接逻辑
        };
    </script>
</body>
</html>
